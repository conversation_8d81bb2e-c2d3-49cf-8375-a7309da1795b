const url = require('url');

// 验证微信公众号文章URL
function validateWechatUrl(urlString) {
    try {
        const parsedUrl = new URL(urlString);
        
        // 检查域名
        const validDomains = [
            'mp.weixin.qq.com',
            'weixin.qq.com'
        ];
        
        if (!validDomains.includes(parsedUrl.hostname)) {
            return false;
        }
        
        // 检查路径
        const validPaths = [
            '/s',
            '/s/',
            '/mp/appmsg/show'
        ];
        
        const isValidPath = validPaths.some(path => 
            parsedUrl.pathname.startsWith(path)
        );
        
        if (!isValidPath) {
            return false;
        }
        
        return true;
        
    } catch (error) {
        return false;
    }
}

// 从URL中提取公众号ID (__biz参数)
async function extractBizFromUrl(urlString) {
    try {
        const parsedUrl = new URL(urlString);

        // 直接从查询参数中获取__biz
        const bizId = parsedUrl.searchParams.get('__biz');
        if (bizId) {
            return bizId;
        }

        // 尝试从hash中获取
        const hash = parsedUrl.hash;
        if (hash) {
            const hashParams = new URLSearchParams(hash.substring(1));
            const hashBizId = hashParams.get('__biz');
            if (hashBizId) {
                return hashBizId;
            }
        }

        // 如果是短链接格式 (如 /s/xxxxx)，需要访问获取重定向URL
        if (parsedUrl.pathname.startsWith('/s/') && parsedUrl.pathname.length > 3) {
            console.log('检测到短链接格式，尝试获取完整URL...');
            return await extractBizFromShortUrl(urlString);
        }

        return null;

    } catch (error) {
        console.error('提取BizID失败:', error);
        return null;
    }
}

// 处理短链接格式
async function extractBizFromShortUrl(shortUrl) {
    try {
        const axios = require('axios');

        // 配置axios实例，正常跟随重定向
        const axiosInstance = axios.create({
            timeout: 15000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        console.log('访问短链接:', shortUrl);
        const response = await axiosInstance.get(shortUrl);

        // 从响应HTML中提取BizID
        if (response.data && typeof response.data === 'string') {
            console.log('从HTML中搜索BizID...');

            // 尝试多种匹配模式
            const patterns = [
                /__biz=([A-Za-z0-9_-]+)/,
                /var biz = "([A-Za-z0-9_-]+)"/,
                /"__biz":"([A-Za-z0-9_-]+)"/,
                /window\.biz\s*=\s*"([A-Za-z0-9_-]+)"/
            ];

            for (const pattern of patterns) {
                const match = response.data.match(pattern);
                if (match && match[1]) {
                    console.log('找到BizID:', match[1]);
                    return match[1];
                }
            }

            // 如果上面的模式都没找到，尝试更宽泛的搜索
            const generalMatch = response.data.match(/[A-Za-z0-9_-]{20,}/g);
            if (generalMatch) {
                // 查找看起来像BizID的字符串（通常以Mz开头）
                for (const candidate of generalMatch) {
                    if (candidate.startsWith('Mz') && candidate.length >= 20) {
                        console.log('可能的BizID:', candidate);
                        return candidate;
                    }
                }
            }
        }

        console.log('未能从HTML中提取到BizID');
        return null;

    } catch (error) {
        console.error('处理短链接失败:', error.message);
        return null;
    }
}

// 构建微信文章搜索URL（用于批量获取）
function buildSearchUrl(bizId, startTime, endTime) {
    // 注意：这个功能需要微信的内部API，实际实现可能需要其他方法
    // 这里提供一个基础框架
    const baseUrl = 'https://mp.weixin.qq.com/mp/profile_ext';
    const params = new URLSearchParams({
        action: 'getmsg',
        __biz: bizId,
        f: 'json',
        offset: '0',
        count: '10',
        is_ok: '1',
        scene: '124',
        uin: '777',
        key: '777',
        pass_ticket: '',
        wxtoken: '',
        appmsg_token: '',
        x5: '0'
    });
    
    return `${baseUrl}?${params.toString()}`;
}

// 解析微信文章URL中的参数
function parseWechatUrlParams(urlString) {
    try {
        const parsedUrl = new URL(urlString);
        const params = {};
        
        // 获取所有查询参数
        parsedUrl.searchParams.forEach((value, key) => {
            params[key] = value;
        });
        
        // 如果有hash，也解析hash中的参数
        if (parsedUrl.hash) {
            const hashParams = new URLSearchParams(parsedUrl.hash.substring(1));
            hashParams.forEach((value, key) => {
                if (!params[key]) {
                    params[key] = value;
                }
            });
        }
        
        return params;
        
    } catch (error) {
        console.error('解析URL参数失败:', error);
        return {};
    }
}

// 清理和标准化URL
function normalizeWechatUrl(urlString) {
    try {
        const parsedUrl = new URL(urlString);
        
        // 移除不必要的参数
        const unnecessaryParams = [
            'chksm',
            'scene',
            'srcid',
            'sharer_sharetime',
            'sharer_shareid',
            'exportkey',
            'pass_ticket',
            'wx_header'
        ];
        
        unnecessaryParams.forEach(param => {
            parsedUrl.searchParams.delete(param);
        });
        
        return parsedUrl.toString();
        
    } catch (error) {
        console.error('标准化URL失败:', error);
        return urlString;
    }
}

module.exports = {
    validateWechatUrl,
    extractBizFromUrl,
    buildSearchUrl,
    parseWechatUrlParams,
    normalizeWechatUrl
};
