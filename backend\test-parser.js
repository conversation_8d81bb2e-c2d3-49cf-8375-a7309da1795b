const { parseWechatArticle, downloadImages, generateMarkdown } = require('./utils/articleParser');

// 测试解析功能
async function testParser() {
    try {
        // 使用一个示例微信文章URL进行测试
        // 注意：这里需要一个真实的微信文章URL
        const testUrl = 'https://mp.weixin.qq.com/s/example'; // 替换为真实URL
        
        console.log('开始测试文章解析...');
        
        // 由于没有真实URL，我们创建一个模拟的测试
        const mockArticleData = {
            title: '测试文章标题',
            author: '测试公众号',
            publishDate: '2025-07-12',
            content: '<p>这是一个测试文章内容。</p><p><img src="https://example.com/test.jpg" alt="测试图片" /></p>',
            images: [
                {
                    url: 'https://example.com/test.jpg',
                    alt: '测试图片',
                    index: 0
                }
            ],
            sourceUrl: testUrl
        };
        
        console.log('模拟文章数据:', mockArticleData);
        
        // 测试图片下载（使用模拟数据）
        console.log('测试图片下载...');
        const imagesInfo = await downloadImages(mockArticleData.images, mockArticleData.title);
        console.log('图片信息:', imagesInfo);
        
        // 测试Markdown生成
        console.log('测试Markdown生成...');
        const markdown = generateMarkdown(mockArticleData, imagesInfo);
        console.log('生成的Markdown:');
        console.log(markdown);
        
        console.log('测试完成！');
        
    } catch (error) {
        console.error('测试失败:', error);
    }
}

// 运行测试
if (require.main === module) {
    testParser();
}

module.exports = { testParser };
