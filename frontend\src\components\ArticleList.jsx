import React, { useState } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Checkbox, 
  Tag, 
  Space, 
  Progress, 
  Typography, 
  Empty,
  Tooltip,
  message
} from 'antd';
import { 
  DownloadOutlined, 
  CheckCircleOutlined, 
  LoadingOutlined, 
  ExclamationCircleOutlined,
  SelectOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Text, Paragraph } = Typography;
const API_BASE_URL = 'http://localhost:3001/api';

const ArticleList = ({ articles, onStatusChange }) => {
  const [selectedArticles, setSelectedArticles] = useState([]);
  const [downloadingArticles, setDownloadingArticles] = useState(new Set());
  const [downloadedArticles, setDownloadedArticles] = useState(new Set());
  const [failedArticles, setFailedArticles] = useState(new Set());
  const [downloadProgress, setDownloadProgress] = useState({});

  // 选择/取消选择文章
  const handleSelectArticle = (articleId, checked) => {
    if (checked) {
      setSelectedArticles(prev => [...prev, articleId]);
    } else {
      setSelectedArticles(prev => prev.filter(id => id !== articleId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedArticles(articles.map((_, index) => index));
    } else {
      setSelectedArticles([]);
    }
  };

  // 下载单篇文章
  const downloadSingleArticle = async (article, index) => {
    const articleId = index;
    
    setDownloadingArticles(prev => new Set([...prev, articleId]));
    setFailedArticles(prev => {
      const newSet = new Set(prev);
      newSet.delete(articleId);
      return newSet;
    });

    try {
      onStatusChange({ 
        type: 'loading', 
        message: `正在下载文章：${article.title}` 
      });

      const response = await axios.post(`${API_BASE_URL}/article/download`, {
        url: article.url
      }, {
        timeout: 60000,
        onDownloadProgress: (progressEvent) => {
          if (progressEvent.lengthComputable) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setDownloadProgress(prev => ({
              ...prev,
              [articleId]: progress
            }));
          }
        }
      });

      if (response.data.success) {
        const { title, markdown, filename } = response.data.data;
        
        // 创建下载链接
        const blob = new Blob([markdown], { type: 'text/markdown;charset=utf-8' });
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);

        setDownloadedArticles(prev => new Set([...prev, articleId]));
        message.success(`《${title}》下载成功！`);
        
        onStatusChange({ 
          type: 'success', 
          message: `文章《${title}》下载成功！` 
        });
      } else {
        throw new Error(response.data.message || '下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      setFailedArticles(prev => new Set([...prev, articleId]));
      
      const errorMessage = error.response?.data?.message || error.message || '下载失败';
      message.error(`下载失败: ${errorMessage}`);
      
      onStatusChange({ 
        type: 'error', 
        message: `下载《${article.title}》失败: ${errorMessage}` 
      });
    } finally {
      setDownloadingArticles(prev => {
        const newSet = new Set(prev);
        newSet.delete(articleId);
        return newSet;
      });
      setDownloadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[articleId];
        return newProgress;
      });
    }
  };

  // 批量下载选中的文章
  const downloadSelectedArticles = async () => {
    if (selectedArticles.length === 0) {
      message.warning('请先选择要下载的文章');
      return;
    }

    onStatusChange({ 
      type: 'loading', 
      message: `开始批量下载 ${selectedArticles.length} 篇文章...` 
    });

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < selectedArticles.length; i++) {
      const articleIndex = selectedArticles[i];
      const article = articles[articleIndex];
      
      try {
        await downloadSingleArticle(article, articleIndex);
        successCount++;
        
        // 添加延迟避免请求过快
        if (i < selectedArticles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        failCount++;
      }
    }

    onStatusChange({ 
      type: successCount > 0 ? 'success' : 'error', 
      message: `批量下载完成！成功: ${successCount}, 失败: ${failCount}` 
    });
  };

  // 获取文章状态图标
  const getStatusIcon = (index) => {
    if (downloadingArticles.has(index)) {
      return <LoadingOutlined style={{ color: '#1890ff' }} />;
    }
    if (downloadedArticles.has(index)) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    }
    if (failedArticles.has(index)) {
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
    return null;
  };

  // 获取文章状态标签
  const getStatusTag = (index) => {
    if (downloadingArticles.has(index)) {
      return <Tag color="processing">下载中</Tag>;
    }
    if (downloadedArticles.has(index)) {
      return <Tag color="success">已下载</Tag>;
    }
    if (failedArticles.has(index)) {
      return <Tag color="error">下载失败</Tag>;
    }
    return <Tag color="default">待下载</Tag>;
  };

  if (!articles || articles.length === 0) {
    return (
      <Card title="文章列表" style={{ height: '100%' }}>
        <Empty 
          description="暂无文章"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  const allSelected = selectedArticles.length === articles.length;
  const indeterminate = selectedArticles.length > 0 && selectedArticles.length < articles.length;

  return (
    <Card 
      title={
        <Space>
          <span>文章列表</span>
          <Tag color="blue">{articles.length} 篇</Tag>
        </Space>
      }
      extra={
        <Space>
          <Checkbox
            indeterminate={indeterminate}
            checked={allSelected}
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            全选
          </Checkbox>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={downloadSelectedArticles}
            disabled={selectedArticles.length === 0}
            size="small"
          >
            下载选中 ({selectedArticles.length})
          </Button>
        </Space>
      }
      style={{ height: '100%' }}
      bodyStyle={{ padding: 0, height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      <List
        dataSource={articles}
        renderItem={(article, index) => (
          <List.Item
            key={index}
            style={{ 
              padding: '12px 16px',
              borderBottom: '1px solid #f0f0f0'
            }}
            actions={[
              <Tooltip title="下载这篇文章">
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  onClick={() => downloadSingleArticle(article, index)}
                  disabled={downloadingArticles.has(index)}
                  size="small"
                />
              </Tooltip>
            ]}
          >
            <List.Item.Meta
              avatar={
                <Space direction="vertical" align="center">
                  <Checkbox
                    checked={selectedArticles.includes(index)}
                    onChange={(e) => handleSelectArticle(index, e.target.checked)}
                  />
                  {getStatusIcon(index)}
                </Space>
              }
              title={
                <Space>
                  <Text strong style={{ fontSize: '14px' }}>
                    {article.title}
                  </Text>
                  {getStatusTag(index)}
                </Space>
              }
              description={
                <div>
                  <Paragraph 
                    ellipsis={{ rows: 2 }} 
                    style={{ margin: 0, fontSize: '12px', color: '#666' }}
                  >
                    {article.digest || '暂无摘要'}
                  </Paragraph>
                  <div style={{ marginTop: '4px', fontSize: '11px', color: '#999' }}>
                    发布时间: {article.publishTime}
                  </div>
                  {downloadingArticles.has(index) && downloadProgress[index] && (
                    <Progress 
                      percent={downloadProgress[index]} 
                      size="small" 
                      style={{ marginTop: '4px' }}
                    />
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      />
    </Card>
  );
};

export default ArticleList;
