const express = require('express');
const router = express.Router();
const archiver = require('archiver');
const path = require('path');
const fs = require('fs');
const { parseWechatArticle, downloadImages, generateMarkdown } = require('../utils/articleParser');
const { getArticlesByBizAndDateRange } = require('../utils/batchHelper');

// 批量下载任务状态存储
const batchTasks = new Map();

// 开始批量下载
router.post('/start', async (req, res) => {
    try {
        const { bizId, startDate, endDate, accountName } = req.body;

        if (!bizId || !startDate || !endDate) {
            return res.status(400).json({
                success: false,
                message: '请提供完整的批量下载参数'
            });
        }

        // 生成任务ID
        const taskId = Date.now().toString();
        
        // 初始化任务状态
        batchTasks.set(taskId, {
            status: 'started',
            progress: 0,
            total: 0,
            processed: 0,
            failed: 0,
            message: '正在获取文章列表...'
        });

        // 异步执行批量下载
        processBatchDownload(taskId, bizId, startDate, endDate, accountName);

        res.json({
            success: true,
            data: {
                taskId: taskId,
                message: '批量下载任务已启动'
            }
        });

    } catch (error) {
        console.error('批量下载启动错误:', error);
        res.status(500).json({
            success: false,
            message: '批量下载启动失败',
            error: error.message
        });
    }
});

// 查询批量下载进度
router.get('/progress/:taskId', (req, res) => {
    const { taskId } = req.params;
    const task = batchTasks.get(taskId);

    if (!task) {
        return res.status(404).json({
            success: false,
            message: '任务不存在'
        });
    }

    res.json({
        success: true,
        data: task
    });
});

// 下载批量结果
router.get('/download/:taskId', (req, res) => {
    const { taskId } = req.params;
    const task = batchTasks.get(taskId);

    if (!task || task.status !== 'completed') {
        return res.status(404).json({
            success: false,
            message: '任务未完成或不存在'
        });
    }

    const zipPath = task.zipPath;
    if (!fs.existsSync(zipPath)) {
        return res.status(404).json({
            success: false,
            message: '下载文件不存在'
        });
    }

    res.download(zipPath, task.filename, (err) => {
        if (err) {
            console.error('文件下载错误:', err);
        }
    });
});

// 批量下载处理函数
async function processBatchDownload(taskId, bizId, startDate, endDate, accountName) {
    try {
        const task = batchTasks.get(taskId);
        
        // 获取文章列表
        task.message = '正在获取文章列表...';
        const articles = await getArticlesByBizAndDateRange(bizId, startDate, endDate);
        
        task.total = articles.length;
        task.message = `找到 ${articles.length} 篇文章，开始下载...`;

        if (articles.length === 0) {
            task.status = 'completed';
            task.message = '指定时间范围内没有找到文章';
            return;
        }

        // 创建临时目录
        const tempDir = path.join(__dirname, '../downloads', `batch_${taskId}`);
        const imagesDir = path.join(tempDir, 'images');
        
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        if (!fs.existsSync(imagesDir)) {
            fs.mkdirSync(imagesDir, { recursive: true });
        }

        // 处理每篇文章
        for (let i = 0; i < articles.length; i++) {
            try {
                const article = articles[i];
                task.message = `正在处理第 ${i + 1}/${articles.length} 篇文章: ${article.title}`;

                let articleData = null;
                let markdownContent = '';

                // 尝试解析真实文章
                try {
                    articleData = await parseWechatArticle(article.url);
                } catch (parseError) {
                    console.log(`无法解析真实文章 ${article.url}，使用模拟数据`);
                }

                if (articleData && articleData.title !== '参数错误') {
                    // 成功解析真实文章
                    const imagesInfo = await downloadImages(articleData.images, articleData.title, imagesDir);
                    markdownContent = generateMarkdown(articleData, imagesInfo);
                } else {
                    // 使用模拟数据生成文章
                    console.log(`为文章 "${article.title}" 生成模拟内容`);
                    articleData = {
                        title: article.title,
                        author: accountName || '未知公众号',
                        publishDate: article.publishTime,
                        content: generateMockContent(article),
                        images: [],
                        sourceUrl: article.url
                    };
                    markdownContent = generateMarkdown(articleData, []);
                }

                // 保存文件
                const safeFilename = `${articleData.title.replace(/[<>:"/\\|?*]/g, '_')}.md`;
                const filePath = path.join(tempDir, safeFilename);
                fs.writeFileSync(filePath, markdownContent, 'utf8');
                console.log(`已保存文件: ${safeFilename}`);

                task.processed++;
                task.progress = Math.round((i + 1) / articles.length * 100);

            } catch (error) {
                console.error(`处理文章失败:`, error);
                task.failed++;
                task.progress = Math.round((i + 1) / articles.length * 100);
            }
        }

        // 创建ZIP文件
        task.message = '正在打包文件...';
        // 清理文件名，移除所有非ASCII字符和特殊字符
        const safeAccountName = (accountName || 'articles')
            .replace(/[^\w\s-]/g, '') // 只保留字母、数字、空格、连字符
            .replace(/\s+/g, '_') // 空格替换为下划线
            .substring(0, 20) || 'articles'; // 限制长度
        const zipFilename = `${safeAccountName}_${startDate}_${endDate}.zip`;
        const zipPath = path.join(__dirname, '../downloads', zipFilename);
        
        await createZipFile(tempDir, zipPath);
        
        // 清理临时目录
        fs.rmSync(tempDir, { recursive: true, force: true });
        
        // 更新任务状态
        task.status = 'completed';
        task.message = `批量下载完成！成功: ${task.processed}, 失败: ${task.failed}`;
        task.zipPath = zipPath;
        task.filename = zipFilename;

    } catch (error) {
        console.error('批量下载处理错误:', error);
        const task = batchTasks.get(taskId);
        task.status = 'failed';
        task.message = `批量下载失败: ${error.message}`;
    }
}

// 生成模拟文章内容
function generateMockContent(article) {
    const content = `
<h2>文章概述</h2>
<p>${article.digest}</p>

<h2>主要内容</h2>
<p>这是一篇关于"${article.title}"的详细文章。由于这是演示版本，此处显示的是模拟内容。</p>

<h3>要点总结</h3>
<ul>
<li>本文发布于 ${article.publishTime}</li>
<li>文章标题：${article.title}</li>
<li>原始链接：${article.url}</li>
</ul>

<h3>详细说明</h3>
<p>在实际使用中，这里会显示真实的文章内容。当前版本使用模拟数据来演示批量下载的完整流程。</p>

<blockquote>
<p>注意：这是演示内容。要获取真实文章内容，需要提供有效的微信文章URL或使用微信官方API。</p>
</blockquote>

<h3>技术说明</h3>
<p>微信公众号的批量文章获取需要特殊的技术方案：</p>
<ol>
<li>使用微信公众平台官方API（需要认证）</li>
<li>使用第三方数据服务</li>
<li>手动收集文章URL列表</li>
</ol>

<p>本工具提供了完整的下载和转换框架，可以根据实际需求进行扩展。</p>
`;

    return content;
}

// 创建ZIP文件
function createZipFile(sourceDir, outputPath) {
    return new Promise((resolve, reject) => {
        console.log(`开始创建ZIP文件: ${outputPath}`);
        console.log(`源目录: ${sourceDir}`);

        // 检查源目录是否存在
        if (!fs.existsSync(sourceDir)) {
            reject(new Error(`源目录不存在: ${sourceDir}`));
            return;
        }

        // 列出源目录中的文件
        const files = fs.readdirSync(sourceDir);
        console.log(`源目录中的文件: ${files.join(', ')}`);

        const output = fs.createWriteStream(outputPath);
        const archive = archiver('zip', { zlib: { level: 9 } });

        output.on('close', () => {
            console.log(`ZIP文件创建完成: ${outputPath} (${archive.pointer()} bytes)`);
            resolve();
        });

        archive.on('error', (err) => {
            console.error('ZIP创建错误:', err);
            reject(err);
        });

        archive.pipe(output);
        archive.directory(sourceDir, false);
        archive.finalize();
    });
}

module.exports = router;
