import React, { useState } from 'react';
import { Layout, Typography, Card, Space, Button, Row, Col } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import SingleDownload from './components/SingleDownload';
import BatchDownload from './components/BatchDownload';
import ManualUrlInput from './components/ManualUrlInput';
import StatusDisplay from './components/StatusDisplay';
import ArticleList from './components/ArticleList';
import HelpModal from './components/HelpModal';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph } = Typography;

function App() {
  const [bizId, setBizId] = useState('');
  const [accountName, setAccountName] = useState('');
  const [helpVisible, setHelpVisible] = useState(false);
  const [articles, setArticles] = useState([]);
  const [status, setStatus] = useState({
    type: 'info',
    message: '请输入微信公众号文章链接开始使用'
  });

  const handleBizExtracted = (data) => {
    setBizId(data.bizId);
    setAccountName(data.accountName);
    setStatus({
      type: 'success',
      message: `已获取公众号ID: ${data.bizId} (${data.accountName})`
    });
  };

  const handleStatusChange = (newStatus) => {
    setStatus(newStatus);
  };

  const handleArticlesLoaded = (articleList) => {
    setArticles(articleList);
    setStatus({
      type: 'success',
      message: `获取到 ${articleList.length} 篇文章，请在右侧选择要下载的文章`
    });
  };

  return (
    <Layout className="layout">
      <Header style={{ background: '#1890ff', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ color: 'white', margin: 0, lineHeight: '64px' }}>
          微信公众号文章下载工具
        </Title>
        <Button
          type="text"
          icon={<QuestionCircleOutlined />}
          style={{ color: 'white' }}
          onClick={() => setHelpVisible(true)}
        >
          使用帮助
        </Button>
      </Header>

      <Content style={{ padding: '24px 50px', minHeight: 'calc(100vh - 134px)' }}>
        <div style={{ maxWidth: 1400, margin: '0 auto' }}>
          <Paragraph style={{ fontSize: '16px', marginBottom: '24px' }}>
            支持单篇文章下载和批量下载，自动转换为Markdown格式，保留图片和格式。
          </Paragraph>

          <Row gutter={24}>
            {/* 左侧操作区域 */}
            <Col xs={24} lg={12}>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* 单篇下载区域 */}
                <Card title="单篇文章下载" size="default">
                  <SingleDownload
                    onBizExtracted={handleBizExtracted}
                    onStatusChange={handleStatusChange}
                    onArticleLoaded={(article) => handleArticlesLoaded([article])}
                  />
                </Card>

                {/* 批量下载区域 */}
                <Card title="批量获取文章" size="default">
                  <BatchDownload
                    bizId={bizId}
                    accountName={accountName}
                    onStatusChange={handleStatusChange}
                    onArticlesLoaded={handleArticlesLoaded}
                  />
                </Card>

                {/* 手动URL输入区域 */}
                <ManualUrlInput
                  onArticlesLoaded={handleArticlesLoaded}
                  onStatusChange={handleStatusChange}
                />

                {/* 状态显示区域 */}
                <StatusDisplay status={status} />
              </Space>
            </Col>

            {/* 右侧文章列表区域 */}
            <Col xs={24} lg={12}>
              <div style={{ height: '600px' }}>
                <ArticleList
                  articles={articles}
                  onStatusChange={handleStatusChange}
                />
              </div>
            </Col>
          </Row>
        </div>
      </Content>

      <Footer style={{ textAlign: 'center' }}>
        微信公众号文章下载工具 ©2025 Created with React + Ant Design
      </Footer>

      <HelpModal
        visible={helpVisible}
        onClose={() => setHelpVisible(false)}
      />
    </Layout>
  );
}

export default App;
