const express = require('express');
const router = express.Router();
const { parseWechatArticle, downloadImages, generateMarkdown } = require('../utils/articleParser');
const { extractBizFromUrl, validateWechatUrl } = require('../utils/urlHelper');

// 单篇文章下载
router.post('/download', async (req, res) => {
    try {
        const { url } = req.body;

        if (!url) {
            return res.status(400).json({
                success: false,
                message: '请提供文章URL'
            });
        }

        // 验证URL格式
        if (!validateWechatUrl(url)) {
            return res.status(400).json({
                success: false,
                message: '无效的微信公众号文章链接'
            });
        }

        // 解析文章内容
        const articleData = await parseWechatArticle(url);
        
        if (!articleData) {
            return res.status(400).json({
                success: false,
                message: '无法解析文章内容，请检查链接是否有效'
            });
        }

        // 下载图片
        const imagesInfo = await downloadImages(articleData.images, articleData.title);

        // 生成Markdown
        const markdownContent = generateMarkdown(articleData, imagesInfo);

        res.json({
            success: true,
            data: {
                title: articleData.title,
                author: articleData.author,
                publishDate: articleData.publishDate,
                markdown: markdownContent,
                filename: `${articleData.title.replace(/[<>:"/\\|?*]/g, '_')}.md`
            }
        });

    } catch (error) {
        console.error('文章下载错误:', error);

        let statusCode = 500;
        let message = '文章下载失败';

        if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            statusCode = 503;
            message = '网络连接失败，请检查网络设置';
        } else if (error.response?.status === 404) {
            statusCode = 404;
            message = '文章不存在或已被删除';
        } else if (error.response?.status === 403) {
            statusCode = 403;
            message = '访问被拒绝，文章可能需要登录或已被限制访问';
        } else if (error.message.includes('无法找到文章内容')) {
            statusCode = 422;
            message = '无法解析文章内容，可能是文章格式不支持或链接无效';
        } else if (error.message.includes('timeout')) {
            statusCode = 408;
            message = '请求超时，请稍后重试';
        } else if (error.message) {
            message = error.message;
        }

        res.status(statusCode).json({
            success: false,
            message: message,
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 获取公众号ID
router.post('/extract-biz', async (req, res) => {
    try {
        const { url } = req.body;

        if (!url) {
            return res.status(400).json({
                success: false,
                message: '请提供文章URL'
            });
        }

        if (!validateWechatUrl(url)) {
            return res.status(400).json({
                success: false,
                message: '无效的微信公众号文章链接'
            });
        }

        const bizId = extractBizFromUrl(url);
        
        if (!bizId) {
            return res.status(400).json({
                success: false,
                message: '无法从URL中提取公众号ID'
            });
        }

        // 同时获取公众号名称
        const articleData = await parseWechatArticle(url);
        
        res.json({
            success: true,
            data: {
                bizId: bizId,
                accountName: articleData ? articleData.author : '未知公众号'
            }
        });

    } catch (error) {
        console.error('提取公众号ID错误:', error);
        res.status(500).json({
            success: false,
            message: '提取公众号ID失败',
            error: error.message
        });
    }
});

module.exports = router;
