const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// 测试API功能
async function testAPI() {
    try {
        console.log('开始测试API...');
        
        // 测试健康检查
        console.log('\n1. 测试健康检查...');
        const healthResponse = await axios.get(`${API_BASE_URL}/health`);
        console.log('健康检查结果:', healthResponse.data);
        
        // 测试URL验证
        console.log('\n2. 测试URL验证...');
        
        // 测试无效URL
        try {
            await axios.post(`${API_BASE_URL}/article/download`, {
                url: 'https://invalid-url.com'
            });
        } catch (error) {
            console.log('无效URL测试通过:', error.response.data.message);
        }
        
        // 测试空URL
        try {
            await axios.post(`${API_BASE_URL}/article/download`, {
                url: ''
            });
        } catch (error) {
            console.log('空URL测试通过:', error.response.data.message);
        }
        
        // 测试BizID提取
        console.log('\n3. 测试BizID提取...');
        const testUrl = 'https://mp.weixin.qq.com/s?__biz=MzI1234567890&mid=**********&idx=1&sn=abcdef123456&chksm=ea123456&scene=21#wechat_redirect';
        
        try {
            const bizResponse = await axios.post(`${API_BASE_URL}/article/extract-biz`, {
                url: testUrl
            });
            console.log('BizID提取结果:', bizResponse.data);
        } catch (error) {
            console.log('BizID提取失败:', error.response?.data?.message || error.message);
        }
        
        // 测试文章下载（使用模拟URL）
        console.log('\n4. 测试文章下载...');
        try {
            const downloadResponse = await axios.post(`${API_BASE_URL}/article/download`, {
                url: testUrl
            });
            console.log('文章下载结果:', {
                success: downloadResponse.data.success,
                title: downloadResponse.data.data?.title,
                author: downloadResponse.data.data?.author,
                markdownLength: downloadResponse.data.data?.markdown?.length
            });
        } catch (error) {
            console.log('文章下载失败:', error.response?.data?.message || error.message);
        }
        
        // 测试批量下载启动
        console.log('\n5. 测试批量下载启动...');
        try {
            const batchResponse = await axios.post(`${API_BASE_URL}/batch/start`, {
                bizId: 'MzI1234567890',
                startDate: '2025-07-01',
                endDate: '2025-07-12',
                accountName: '测试公众号'
            });
            console.log('批量下载启动结果:', batchResponse.data);
            
            // 如果启动成功，测试进度查询
            if (batchResponse.data.success) {
                const taskId = batchResponse.data.data.taskId;
                console.log('\n6. 测试进度查询...');
                
                setTimeout(async () => {
                    try {
                        const progressResponse = await axios.get(`${API_BASE_URL}/batch/progress/${taskId}`);
                        console.log('进度查询结果:', progressResponse.data);
                    } catch (error) {
                        console.log('进度查询失败:', error.response?.data?.message || error.message);
                    }
                }, 1000);
            }
        } catch (error) {
            console.log('批量下载启动失败:', error.response?.data?.message || error.message);
        }
        
        console.log('\nAPI测试完成！');
        
    } catch (error) {
        console.error('API测试失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testAPI();
}

module.exports = { testAPI };
