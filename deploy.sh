#!/bin/bash

# 微信公众号文章下载工具部署脚本

echo "开始部署微信公众号文章下载工具..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止现有服务
echo "停止现有服务..."
docker-compose down

# 构建并启动服务
echo "构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 测试后端API
echo "测试后端API..."
if curl -f http://localhost:3001/api/health > /dev/null 2>&1; then
    echo "✅ 后端API正常运行"
else
    echo "❌ 后端API启动失败"
    docker-compose logs backend
    exit 1
fi

# 测试前端
echo "测试前端..."
if curl -f http://localhost:80 > /dev/null 2>&1; then
    echo "✅ 前端正常运行"
else
    echo "❌ 前端启动失败"
    docker-compose logs frontend
    exit 1
fi

echo ""
echo "🎉 部署完成！"
echo "前端地址: http://localhost"
echo "后端API: http://localhost:3001/api"
echo ""
echo "查看日志: docker-compose logs -f"
echo "停止服务: docker-compose down"
