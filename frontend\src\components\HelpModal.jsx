import React from 'react';
import { Modal, Typography, Divider, Steps, Alert } from 'antd';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

const HelpModal = ({ visible, onClose }) => {
  return (
    <Modal
      title="使用帮助"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        <Alert
          message="重要提示"
          description="本工具仅用于个人学习和备份用途，请遵守相关法律法规和平台使用条款。"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Title level={4}>功能介绍</Title>
        <Paragraph>
          微信公众号文章下载工具支持将微信公众号文章转换为Markdown格式，并自动下载文章中的图片。
          主要功能包括：
        </Paragraph>
        <ul>
          <li>单篇文章下载：快速下载指定文章</li>
          <li>批量下载：根据公众号和时间范围批量下载</li>
          <li>图片自动下载：保存文章中的所有图片到本地</li>
          <li>Markdown格式：保持文章格式，便于后续编辑</li>
        </ul>

        <Divider />

        <Title level={4}>单篇文章下载步骤</Title>
        <Steps direction="vertical" size="small">
          <Step
            title="复制文章链接"
            description="在微信中打开文章，点击右上角分享按钮，选择'复制链接'"
          />
          <Step
            title="粘贴链接"
            description="将复制的链接粘贴到'文章链接'输入框中"
          />
          <Step
            title="下载文章"
            description="点击'下载当前文章'按钮，等待处理完成后自动下载"
          />
        </Steps>

        <Divider />

        <Title level={4}>批量下载步骤</Title>
        <Steps direction="vertical" size="small">
          <Step
            title="获取公众号ID"
            description="粘贴该公众号的任意一篇文章链接，点击'获取公众号ID'按钮"
          />
          <Step
            title="选择日期范围"
            description="设置要下载文章的起始和结束日期"
          />
          <Step
            title="开始批量下载"
            description="点击'开始批量下载'按钮，系统会自动处理并打包所有文章"
          />
        </Steps>

        <Divider />

        <Title level={4}>支持的链接格式</Title>
        <Paragraph>
          <Text code>https://mp.weixin.qq.com/s/xxxxxxxxxx</Text>
          <br />
          <Text code>https://mp.weixin.qq.com/s?__biz=xxx&mid=xxx&idx=xxx&sn=xxx</Text>
        </Paragraph>

        <Divider />

        <Title level={4}>常见问题</Title>
        <Paragraph>
          <Text strong>Q: 为什么有些文章下载失败？</Text>
          <br />
          A: 可能的原因包括：文章已被删除、需要登录查看、网络连接问题等。
        </Paragraph>
        <Paragraph>
          <Text strong>Q: 批量下载需要多长时间？</Text>
          <br />
          A: 取决于文章数量和网络状况，通常每篇文章需要2-5秒处理时间。
        </Paragraph>
        <Paragraph>
          <Text strong>Q: 下载的文件在哪里？</Text>
          <br />
          A: 单篇文章会直接下载到浏览器默认下载目录，批量下载会生成ZIP压缩包。
        </Paragraph>

        <Divider />

        <Title level={4}>技术说明</Title>
        <Paragraph>
          <ul>
            <li>文章内容使用Markdown格式保存，兼容大多数编辑器</li>
            <li>图片会自动下载并使用相对路径引用</li>
            <li>支持文章元数据（标题、作者、发布时间等）</li>
            <li>批量下载结果会打包为ZIP文件，包含所有文章和图片</li>
          </ul>
        </Paragraph>
      </div>
    </Modal>
  );
};

export default HelpModal;
