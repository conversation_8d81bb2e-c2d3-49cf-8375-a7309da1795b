# 微信公众号文章下载工具 (PC端专用)

一个功能强大的微信公众号文章下载工具，专为PC端设计，支持单篇文章下载和批量下载，自动转换为Markdown格式并保存图片。

## 功能特性

- ✅ **单篇文章下载**：快速下载指定微信公众号文章
- ✅ **批量获取文章**：根据公众号ID和时间范围获取文章列表
- ✅ **手动URL输入**：支持手动输入文章URL列表获取真实数据
- ✅ **选择性下载**：在文章列表中选择需要的文章进行下载
- ✅ **Markdown转换**：自动将HTML内容转换为Markdown格式
- ✅ **图片下载**：自动下载并保存文章中的所有图片
- ✅ **元数据保存**：保留文章标题、作者、发布时间等信息
- ✅ **实时状态**：显示每篇文章的下载状态和进度
- ✅ **PC端优化**：专为PC端设计的界面布局
- ✅ **错误处理**：完善的错误处理和用户提示

## 技术栈

### 前端
- React 19
- Ant Design 5
- Axios
- Vite

### 后端
- Node.js
- Express 4
- Cheerio (HTML解析)
- Turndown (HTML转Markdown)
- Archiver (ZIP打包)

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd WXdownload
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **安装前端依赖**
```bash
cd ../frontend
npm install
```

4. **启动后端服务**
```bash
cd ../backend
npm run dev
```
后端服务将运行在 http://localhost:3001

5. **启动前端服务**
```bash
cd ../frontend
npm run dev
```
前端服务将运行在 http://localhost:5176

## 使用方法

### 单篇文章下载

1. 在微信中打开要下载的文章
2. 点击右上角分享按钮，选择"复制链接"
3. 将链接粘贴到工具的"文章链接"输入框
4. 点击"下载当前文章"按钮
5. 等待处理完成后，文章将自动下载为Markdown文件

### 批量获取和下载

#### 方法1：自动获取（可能受限）
1. 首先获取公众号ID：
   - 粘贴该公众号的任意一篇文章链接
   - 点击"获取公众号ID"按钮
2. 选择要获取的日期范围
3. 点击"获取文章列表"按钮
4. 在右侧文章列表中选择要下载的文章

#### 方法2：手动输入URL（推荐）
1. 手动收集文章URL（每行一个）
2. 粘贴到"手动输入文章URL"区域
3. 点击"处理URL列表"按钮
4. 在右侧文章列表中选择要下载的文章

## API接口

### 文章相关接口

- `POST /api/article/download` - 下载单篇文章
- `POST /api/article/extract-biz` - 提取公众号ID

### 批量下载接口

- `POST /api/batch/start` - 启动批量下载任务
- `GET /api/batch/progress/:taskId` - 查询下载进度
- `GET /api/batch/download/:taskId` - 下载批量结果

### 其他接口

- `GET /api/health` - 健康检查

## 项目结构

```
WXdownload/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── App.jsx         # 主应用组件
│   │   └── main.jsx        # 入口文件
│   └── package.json
├── backend/                  # 后端项目
│   ├── routes/              # 路由文件
│   ├── utils/               # 工具函数
│   ├── downloads/           # 下载文件目录
│   ├── server.js           # 服务器入口
│   └── package.json
└── README.md
```

## 开发说明

### 测试

运行后端测试：
```bash
cd backend
node test-parser.js  # 测试解析功能
node test-api.js     # 测试API接口
```

### 构建部署

构建前端：
```bash
cd frontend
npm run build
```

生产环境运行后端：
```bash
cd backend
npm start
```

## 注意事项

1. **合法使用**：本工具仅用于个人学习和备份用途，请遵守相关法律法规
2. **网络限制**：由于微信的反爬虫机制，部分功能可能受限
3. **批量下载**：当前批量下载使用模拟数据，实际部署需要实现真实的文章获取逻辑
4. **图片下载**：某些图片可能因为防盗链等原因下载失败

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0 (2025-07-12)
- 初始版本发布
- 实现单篇文章下载功能
- 实现批量下载框架
- 添加Markdown转换和图片下载
- 完善错误处理和用户界面
