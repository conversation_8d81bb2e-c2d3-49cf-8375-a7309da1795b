version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
    volumes:
      - ./backend/downloads:/app/downloads
    restart: unless-stopped
    networks:
      - wxdownload-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - wxdownload-network

networks:
  wxdownload-network:
    driver: bridge

volumes:
  downloads:
    driver: local
