import React, { useState, useEffect } from 'react';
import { Input, Button, DatePicker, Space, message, Row, Col, Typography } from 'antd';
import { PlayCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Text } = Typography;

const API_BASE_URL = 'http://localhost:3001/api';

const BatchDownload = ({ bizId, accountName, onStatusChange, onArticlesLoaded }) => {
  const [localBizId, setLocalBizId] = useState('');
  const [localAccountName, setLocalAccountName] = useState('');
  const [dateRange, setDateRange] = useState([]);
  const [loading, setLoading] = useState(false);

  // 同步外部传入的bizId和accountName
  useEffect(() => {
    if (bizId) {
      setLocalBizId(bizId);
    }
    if (accountName) {
      setLocalAccountName(accountName);
    }
  }, [bizId, accountName]);

  // 获取文章列表
  const handleGetArticles = async () => {
    if (!localBizId.trim()) {
      message.error('请先获取公众号ID或手动输入');
      return;
    }

    if (!dateRange || dateRange.length !== 2) {
      message.error('请选择日期范围');
      return;
    }

    // 验证日期范围
    const startDate = dateRange[0].format('YYYY-MM-DD');
    const endDate = dateRange[1].format('YYYY-MM-DD');
    const daysDiff = dateRange[1].diff(dateRange[0], 'days');

    if (daysDiff < 0) {
      message.error('结束日期不能早于开始日期');
      return;
    }

    if (daysDiff > 365) {
      message.warning('日期范围超过一年，可能需要较长时间处理');
    }

    setLoading(true);
    onStatusChange({ type: 'loading', message: '正在获取文章列表...' });

    try {
      const response = await axios.post(`${API_BASE_URL}/article/list`, {
        bizId: localBizId.trim(),
        startDate,
        endDate
      }, {
        timeout: 30000
      });

      if (response.data.success) {
        const { articles, total } = response.data.data;

        if (onArticlesLoaded) {
          onArticlesLoaded(articles);
        }

        message.success(`成功获取 ${total} 篇文章！`);
        onStatusChange({
          type: 'success',
          message: `成功获取 ${total} 篇文章，请在右侧选择要下载的文章`
        });
      } else {
        throw new Error(response.data.message || '获取失败');
      }
    } catch (error) {
      console.error('获取文章列表失败:', error);
      let errorMessage = '获取文章列表失败';

      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络连接';
      } else if (error.response?.status === 400) {
        errorMessage = error.response.data?.message || '请求参数错误';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
      onStatusChange({
        type: 'error',
        message: `获取文章列表失败: ${errorMessage}`
      });
    } finally {
      setLoading(false);
    }
  };



  // 预设日期范围
  const setPresetDateRange = (days) => {
    const end = dayjs();
    const start = end.subtract(days, 'day');
    setDateRange([start, end]);
  };

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      <Row gutter={16}>
        <Col span={12}>
          <Input
            placeholder="公众号ID (__biz参数)"
            value={localBizId}
            onChange={(e) => setLocalBizId(e.target.value)}
            addonBefore="公众号ID"
          />
        </Col>
        <Col span={12}>
          <Input
            placeholder="公众号名称（可选）"
            value={localAccountName}
            onChange={(e) => setLocalAccountName(e.target.value)}
            addonBefore="公众号名称"
          />
        </Col>
      </Row>

      <div>
        <Text strong style={{ marginRight: 16 }}>选择日期范围：</Text>
        <RangePicker
          value={dateRange}
          onChange={setDateRange}
          format="YYYY-MM-DD"
          placeholder={['开始日期', '结束日期']}
          style={{ marginRight: 16 }}
        />
        <Space>
          <Button size="small" onClick={() => setPresetDateRange(7)}>
            最近7天
          </Button>
          <Button size="small" onClick={() => setPresetDateRange(30)}>
            最近30天
          </Button>
          <Button size="small" onClick={() => setPresetDateRange(90)}>
            最近3个月
          </Button>
        </Space>
      </div>

      <Button
        type="primary"
        icon={<PlayCircleOutlined />}
        loading={loading}
        onClick={handleGetArticles}
        size="large"
        block
        disabled={!localBizId.trim() || !dateRange || dateRange.length !== 2}
      >
        获取文章列表
      </Button>

      <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.5' }}>
        <p><strong>批量获取说明：</strong></p>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>需要先通过单篇下载获取公众号ID，或手动输入__biz参数</li>
          <li>选择要获取的日期范围，系统会查找该时间段内的所有文章</li>
          <li>文章列表将显示在右侧，您可以选择性下载需要的文章</li>
          <li>支持单篇下载和批量下载，无需等待打包</li>
        </ul>
      </div>
    </Space>
  );
};

export default BatchDownload;
