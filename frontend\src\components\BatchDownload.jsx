import React, { useState, useEffect } from 'react';
import { Input, Button, DatePicker, Space, message, Row, Col, Progress, Typography } from 'antd';
import { DownloadOutlined, PlayCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Text } = Typography;

const API_BASE_URL = 'http://localhost:3001/api';

const BatchDownload = ({ bizId, accountName, onStatusChange }) => {
  const [localBizId, setLocalBizId] = useState('');
  const [localAccountName, setLocalAccountName] = useState('');
  const [dateRange, setDateRange] = useState([]);
  const [loading, setLoading] = useState(false);
  const [taskId, setTaskId] = useState('');
  const [progress, setProgress] = useState(null);

  // 同步外部传入的bizId和accountName
  useEffect(() => {
    if (bizId) {
      setLocalBizId(bizId);
    }
    if (accountName) {
      setLocalAccountName(accountName);
    }
  }, [bizId, accountName]);

  // 轮询任务进度
  useEffect(() => {
    let interval;
    if (taskId && loading) {
      interval = setInterval(async () => {
        try {
          const response = await axios.get(`${API_BASE_URL}/batch/progress/${taskId}`);
          if (response.data.success) {
            const taskData = response.data.data;
            setProgress(taskData);
            
            onStatusChange({
              type: taskData.status === 'failed' ? 'error' : 'loading',
              message: taskData.message
            });

            if (taskData.status === 'completed') {
              setLoading(false);
              clearInterval(interval);
              
              // 自动下载结果
              const downloadUrl = `${API_BASE_URL}/batch/download/${taskId}`;
              window.open(downloadUrl, '_blank');
              
              message.success('批量下载完成！');
              onStatusChange({
                type: 'success',
                message: `批量下载完成！成功: ${taskData.processed}, 失败: ${taskData.failed}`
              });
            } else if (taskData.status === 'failed') {
              setLoading(false);
              clearInterval(interval);
            }
          }
        } catch (error) {
          console.error('获取进度失败:', error);
        }
      }, 2000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [taskId, loading, onStatusChange]);

  // 开始批量下载
  const handleBatchDownload = async () => {
    if (!localBizId.trim()) {
      message.error('请先获取公众号ID或手动输入');
      return;
    }

    if (!dateRange || dateRange.length !== 2) {
      message.error('请选择日期范围');
      return;
    }

    // 验证日期范围
    const startDate = dateRange[0].format('YYYY-MM-DD');
    const endDate = dateRange[1].format('YYYY-MM-DD');
    const daysDiff = dateRange[1].diff(dateRange[0], 'days');

    if (daysDiff < 0) {
      message.error('结束日期不能早于开始日期');
      return;
    }

    if (daysDiff > 365) {
      message.warning('日期范围超过一年，可能需要较长时间处理');
    }

    setLoading(true);
    setProgress(null);
    setTaskId('');
    onStatusChange({ type: 'loading', message: '正在启动批量下载任务...' });

    try {
      const response = await axios.post(`${API_BASE_URL}/batch/start`, {
        bizId: localBizId.trim(),
        startDate,
        endDate,
        accountName: localAccountName || '未知公众号'
      }, {
        timeout: 30000 // 30秒超时
      });

      if (response.data.success) {
        const newTaskId = response.data.data.taskId;
        setTaskId(newTaskId);
        message.success('批量下载任务已启动，正在处理中...');
        onStatusChange({
          type: 'loading',
          message: `批量下载任务已启动 (任务ID: ${newTaskId})，正在获取文章列表...`
        });
      } else {
        throw new Error(response.data.message || '启动失败');
      }
    } catch (error) {
      console.error('启动批量下载失败:', error);
      let errorMessage = '启动失败';

      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络连接';
      } else if (error.response?.status === 400) {
        errorMessage = error.response.data?.message || '请求参数错误';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
      onStatusChange({
        type: 'error',
        message: `启动批量下载失败: ${errorMessage}`
      });
      setLoading(false);
      setTaskId('');
      setProgress(null);
    }
  };

  // 预设日期范围
  const setPresetDateRange = (days) => {
    const end = dayjs();
    const start = end.subtract(days, 'day');
    setDateRange([start, end]);
  };

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      <Row gutter={16}>
        <Col span={12}>
          <Input
            placeholder="公众号ID (__biz参数)"
            value={localBizId}
            onChange={(e) => setLocalBizId(e.target.value)}
            addonBefore="公众号ID"
          />
        </Col>
        <Col span={12}>
          <Input
            placeholder="公众号名称（可选）"
            value={localAccountName}
            onChange={(e) => setLocalAccountName(e.target.value)}
            addonBefore="公众号名称"
          />
        </Col>
      </Row>

      <div>
        <Text strong style={{ marginRight: 16 }}>选择日期范围：</Text>
        <RangePicker
          value={dateRange}
          onChange={setDateRange}
          format="YYYY-MM-DD"
          placeholder={['开始日期', '结束日期']}
          style={{ marginRight: 16 }}
        />
        <Space>
          <Button size="small" onClick={() => setPresetDateRange(7)}>
            最近7天
          </Button>
          <Button size="small" onClick={() => setPresetDateRange(30)}>
            最近30天
          </Button>
          <Button size="small" onClick={() => setPresetDateRange(90)}>
            最近3个月
          </Button>
        </Space>
      </div>

      <Button
        type="primary"
        icon={<PlayCircleOutlined />}
        loading={loading}
        onClick={handleBatchDownload}
        size="large"
        block
        disabled={!localBizId.trim() || !dateRange || dateRange.length !== 2}
      >
        开始批量下载
      </Button>

      {progress && (
        <div style={{ marginTop: 16 }}>
          <Progress
            percent={progress.progress}
            status={progress.status === 'failed' ? 'exception' : 'active'}
            format={() => `${progress.processed || 0}/${progress.total || 0}`}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {progress.message}
          </Text>
        </div>
      )}

      <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.5' }}>
        <p><strong>批量下载说明：</strong></p>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>需要先通过单篇下载获取公众号ID，或手动输入__biz参数</li>
          <li>选择要下载的日期范围，系统会自动查找该时间段内的所有文章</li>
          <li>下载完成后会自动打包为ZIP文件，包含所有文章和图片</li>
          <li>注意：批量下载可能需要较长时间，请耐心等待</li>
        </ul>
      </div>
    </Space>
  );
};

export default BatchDownload;
