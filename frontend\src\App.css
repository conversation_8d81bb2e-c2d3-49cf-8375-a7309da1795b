.layout {
  min-height: 100vh;
}

.ant-layout-header {
  display: flex;
  align-items: center;
  padding: 0 50px;
}

.ant-card {
  margin-bottom: 16px;
}

.ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
}

.ant-progress-text {
  font-size: 12px;
}

/* 自定义样式 */
.status-card {
  border-left: 4px solid #1890ff;
}

.download-area {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
  margin: 16px 0;
}

.help-text {
  color: #666;
  font-size: 12px;
  line-height: 1.6;
  margin-top: 12px;
}

.help-text ul {
  margin: 8px 0;
  padding-left: 20px;
}

.help-text li {
  margin-bottom: 4px;
}

/* PC端专用样式 */
.ant-layout-header {
  padding: 0 50px;
  min-width: 1200px;
}

.ant-layout-content {
  padding: 24px 50px !important;
  min-width: 1200px;
}

/* 固定最小宽度，确保PC端体验 */
body {
  min-width: 1200px;
  overflow-x: auto;
}
