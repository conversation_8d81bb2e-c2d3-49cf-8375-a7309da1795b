import React, { useState } from 'react';
import { Input, Button, Space, message, Row, Col } from 'antd';
import { DownloadOutlined, LinkOutlined } from '@ant-design/icons';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001/api';

const SingleDownload = ({ onBizExtracted, onStatusChange, onArticleLoaded }) => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [extracting, setExtracting] = useState(false);

  // 下载单篇文章
  const handleDownload = async () => {
    if (!url.trim()) {
      message.error('请输入文章链接');
      return;
    }

    // 基本URL格式验证
    if (!url.includes('mp.weixin.qq.com')) {
      message.error('请输入有效的微信公众号文章链接');
      return;
    }

    setLoading(true);
    onStatusChange({ type: 'loading', message: '正在解析文章内容...' });

    try {
      const response = await axios.post(`${API_BASE_URL}/article/download`, {
        url: url.trim()
      }, {
        timeout: 60000 // 60秒超时
      });

      if (response.data.success) {
        const { title, author, publishDate, markdown, filename } = response.data.data;

        if (!markdown || markdown.length < 100) {
          message.warning('文章内容可能不完整，请检查链接是否正确');
        }

        // 将文章信息传递给父组件，显示在文章列表中
        if (onArticleLoaded) {
          onArticleLoaded({
            title: title,
            url: url.trim(),
            publishTime: publishDate,
            digest: `文章大小: ${(markdown.length / 1024).toFixed(1)}KB`,
            author: author
          });
        }

        // 创建下载链接
        const blob = new Blob([markdown], { type: 'text/markdown;charset=utf-8' });
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);

        message.success('文章下载成功！');
        onStatusChange({
          type: 'success',
          message: `文章《${title}》下载成功！文件大小: ${(markdown.length / 1024).toFixed(1)}KB`
        });
      } else {
        throw new Error(response.data.message || '下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      let errorMessage = '下载失败';

      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络连接或稍后重试';
      } else if (error.response?.status === 404) {
        errorMessage = '文章不存在或已被删除';
      } else if (error.response?.status === 403) {
        errorMessage = '访问被拒绝，可能是文章需要登录或已被限制访问';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
      onStatusChange({
        type: 'error',
        message: `下载失败: ${errorMessage}`
      });
    } finally {
      setLoading(false);
    }
  };

  // 提取公众号ID
  const handleExtractBiz = async () => {
    if (!url.trim()) {
      message.error('请输入文章链接');
      return;
    }

    setExtracting(true);
    onStatusChange({ type: 'loading', message: '正在提取公众号ID...' });

    try {
      const response = await axios.post(`${API_BASE_URL}/article/extract-biz`, {
        url: url.trim()
      });

      if (response.data.success) {
        const { bizId, accountName } = response.data.data;
        message.success(`成功获取公众号ID: ${bizId}`);
        onBizExtracted({ bizId, accountName });
      } else {
        throw new Error(response.data.message || '提取失败');
      }
    } catch (error) {
      console.error('提取公众号ID失败:', error);
      const errorMessage = error.response?.data?.message || error.message || '提取失败';
      message.error(errorMessage);
      onStatusChange({ 
        type: 'error', 
        message: `提取公众号ID失败: ${errorMessage}` 
      });
    } finally {
      setExtracting(false);
    }
  };

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      <Input.TextArea
        placeholder="请在此处粘贴微信公众号文章链接&#10;例如: https://mp.weixin.qq.com/s/xxxxxxxxxx"
        value={url}
        onChange={(e) => setUrl(e.target.value)}
        rows={3}
        style={{ fontSize: '14px' }}
      />
      
      <Row gutter={16}>
        <Col span={12}>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            loading={loading}
            onClick={handleDownload}
            size="large"
            block
          >
            下载当前文章
          </Button>
        </Col>
        <Col span={12}>
          <Button
            icon={<LinkOutlined />}
            loading={extracting}
            onClick={handleExtractBiz}
            size="large"
            block
          >
            获取公众号ID
          </Button>
        </Col>
      </Row>
      
      <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.5' }}>
        <p><strong>使用说明：</strong></p>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>粘贴微信公众号文章链接，点击"下载当前文章"即可下载为Markdown格式</li>
          <li>点击"获取公众号ID"可以提取该公众号的ID，用于批量下载</li>
          <li>下载的文件包含文章内容和图片，图片会自动保存到本地</li>
        </ul>
      </div>
    </Space>
  );
};

export default SingleDownload;
