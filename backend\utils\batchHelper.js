const axios = require('axios');

// 配置axios
const axiosInstance = axios.create({
    timeout: 30000,
    headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
});

// 根据公众号ID和日期范围获取文章列表
async function getArticlesByBizAndDateRange(bizId, startDate, endDate) {
    try {
        console.log(`获取公众号 ${bizId} 从 ${startDate} 到 ${endDate} 的文章`);

        // 注意：由于微信的反爬虫机制，真实的批量获取需要特殊的方法
        // 这里提供一个改进的模拟实现，用于演示完整的工作流程

        // 在实际项目中，您可能需要：
        // 1. 使用微信公众平台的官方API（需要认证）
        // 2. 使用第三方数据服务
        // 3. 手动提供文章URL列表

        console.log('警告：当前使用模拟数据进行演示。实际使用时需要真实的文章获取方法。');

        // 生成更真实的模拟数据
        const mockArticles = await generateMockArticles(bizId, startDate, endDate);

        console.log(`找到 ${mockArticles.length} 篇符合条件的文章`);
        return mockArticles;

    } catch (error) {
        console.error('获取文章列表失败:', error);
        throw error;
    }
}

// 生成模拟文章数据（用于演示）
async function generateMockArticles(bizId, startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const daysDiff = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

    // 根据时间范围生成更真实的文章数量
    // 活跃公众号：每天0.5-2篇文章
    let articleCount;
    if (daysDiff <= 7) {
        // 一周内：每天1-2篇
        articleCount = Math.max(5, Math.floor(daysDiff * 1.2 + Math.random() * daysDiff * 0.8));
    } else if (daysDiff <= 30) {
        // 一个月内：每天0.8-1.5篇
        articleCount = Math.max(10, Math.floor(daysDiff * 0.8 + Math.random() * daysDiff * 0.7));
    } else {
        // 更长时间：每天0.5-1篇
        articleCount = Math.max(15, Math.floor(daysDiff * 0.5 + Math.random() * daysDiff * 0.5));
    }

    // 限制最大数量避免生成过多
    articleCount = Math.min(articleCount, 100);

    console.log(`为时间范围 ${daysDiff} 天生成 ${articleCount} 篇文章`);
    const articles = [];

    const sampleTitles = [
        'AI技术发展趋势分析',
        '前端开发最佳实践',
        '数据科学入门指南',
        '云计算架构设计',
        '机器学习算法详解',
        '区块链技术应用',
        '移动应用开发技巧',
        '网络安全防护策略',
        '大数据处理方案',
        '软件工程管理',
        'ChatGPT使用技巧分享',
        '深度学习模型优化',
        'React性能优化指南',
        'Python数据分析实战',
        'Docker容器化部署',
        'Kubernetes集群管理',
        'Vue3新特性解析',
        'TypeScript开发指南',
        'Node.js后端开发',
        'MySQL数据库优化',
        'Redis缓存策略',
        '微服务架构设计',
        'API接口设计规范',
        '代码重构技巧',
        '敏捷开发实践',
        '产品设计思维',
        '用户体验优化',
        '数字化转型案例',
        '创业经验分享',
        '技术团队管理'
    ];

    // 添加一些标题修饰词让文章更真实
    const titlePrefixes = ['深度解析', '实战指南', '最新', '详细教程', '完整攻略', '经验分享', '案例研究', '技术解读'];
    const titleSuffixes = ['| 值得收藏', '| 干货满满', '| 实用技巧', '| 深度好文', '| 必看推荐', '| 精华总结'];

    for (let i = 0; i < articleCount; i++) {
        // 在时间范围内随机分布文章发布时间，但确保更近期的文章更多
        let randomDays;
        if (Math.random() < 0.6) {
            // 60%的文章在最近1/3时间内
            randomDays = Math.floor(Math.random() * Math.ceil(daysDiff / 3));
        } else {
            // 40%的文章在其余时间内
            randomDays = Math.floor(Math.random() * daysDiff);
        }
        const publishDate = new Date(end.getTime() - randomDays * 24 * 60 * 60 * 1000);

        // 随机选择标题并添加修饰
        const titleIndex = Math.floor(Math.random() * sampleTitles.length);
        let title = sampleTitles[titleIndex];

        // 30%概率添加前缀
        if (Math.random() < 0.3) {
            const prefixIndex = Math.floor(Math.random() * titlePrefixes.length);
            title = `${titlePrefixes[prefixIndex]}：${title}`;
        }

        // 20%概率添加后缀
        if (Math.random() < 0.2) {
            const suffixIndex = Math.floor(Math.random() * titleSuffixes.length);
            title = `${title} ${titleSuffixes[suffixIndex]}`;
        }

        // 生成更真实的摘要
        const digestTemplates = [
            `本文详细介绍了${sampleTitles[titleIndex]}的核心要点和实践方法。`,
            `分享${sampleTitles[titleIndex]}的最新进展和实用技巧。`,
            `深入探讨${sampleTitles[titleIndex]}的应用场景和解决方案。`,
            `从零开始学习${sampleTitles[titleIndex]}，包含完整代码示例。`,
            `${sampleTitles[titleIndex]}的完整指南，适合初学者和进阶开发者。`
        ];
        const digest = digestTemplates[Math.floor(Math.random() * digestTemplates.length)];

        articles.push({
            title: title,
            url: `https://mp.weixin.qq.com/s?__biz=${bizId}&mid=${2247484000 + i}&idx=1&sn=${generateRandomSn()}&chksm=ea${Math.random().toString(36).substr(2, 6)}`,
            publishTime: publishDate.toISOString().split('T')[0],
            digest: digest
        });
    }

    // 按发布时间排序（最新的在前）
    articles.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime));

    return articles;
}

// 生成随机的sn参数
function generateRandomSn() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 尝试通过微信搜索API获取文章（需要进一步实现）
async function searchArticlesByBiz(bizId, offset = 0, count = 10) {
    try {
        // 这个方法需要有效的微信token和参数
        // 实际实现时需要处理认证、cookie等问题
        
        const searchUrl = 'https://mp.weixin.qq.com/mp/profile_ext';
        const params = {
            action: 'getmsg',
            __biz: bizId,
            f: 'json',
            offset: offset,
            count: count,
            is_ok: '1',
            scene: '124'
            // 还需要其他认证参数
        };
        
        // 注意：这个请求在没有正确认证的情况下会失败
        // const response = await axiosInstance.get(searchUrl, { params });
        // return response.data;
        
        // 返回空结果，避免实际请求失败
        return {
            general_msg_list: JSON.stringify({
                list: []
            })
        };
        
    } catch (error) {
        console.error('搜索文章失败:', error);
        return {
            general_msg_list: JSON.stringify({
                list: []
            })
        };
    }
}

// 解析搜索结果
function parseSearchResults(searchData) {
    try {
        if (!searchData.general_msg_list) {
            return [];
        }
        
        const msgList = JSON.parse(searchData.general_msg_list);
        const articles = [];
        
        if (msgList.list && Array.isArray(msgList.list)) {
            msgList.list.forEach(item => {
                if (item.app_msg_ext_info) {
                    const article = item.app_msg_ext_info;
                    articles.push({
                        title: article.title,
                        url: article.content_url,
                        publishTime: new Date(item.comm_msg_info.datetime * 1000).toISOString().split('T')[0],
                        digest: article.digest,
                        author: article.author
                    });
                    
                    // 处理多图文消息
                    if (article.multi_app_msg_item_list) {
                        article.multi_app_msg_item_list.forEach(subArticle => {
                            articles.push({
                                title: subArticle.title,
                                url: subArticle.content_url,
                                publishTime: new Date(item.comm_msg_info.datetime * 1000).toISOString().split('T')[0],
                                digest: subArticle.digest,
                                author: subArticle.author
                            });
                        });
                    }
                }
            });
        }
        
        return articles;
        
    } catch (error) {
        console.error('解析搜索结果失败:', error);
        return [];
    }
}

// 获取所有文章（分页处理）
async function getAllArticlesByBiz(bizId, startDate, endDate, maxPages = 10) {
    const allArticles = [];
    let offset = 0;
    const count = 10;
    
    for (let page = 0; page < maxPages; page++) {
        try {
            const searchData = await searchArticlesByBiz(bizId, offset, count);
            const articles = parseSearchResults(searchData);
            
            if (articles.length === 0) {
                break; // 没有更多文章
            }
            
            // 过滤日期范围
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            const filteredArticles = articles.filter(article => {
                const publishDate = new Date(article.publishTime);
                return publishDate >= start && publishDate <= end;
            });
            
            allArticles.push(...filteredArticles);
            
            // 如果最早的文章已经超出日期范围，停止搜索
            const oldestArticle = articles[articles.length - 1];
            if (oldestArticle && new Date(oldestArticle.publishTime) < start) {
                break;
            }
            
            offset += count;
            
            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.error(`获取第 ${page + 1} 页文章失败:`, error);
            break;
        }
    }
    
    return allArticles;
}

module.exports = {
    getArticlesByBizAndDateRange,
    searchArticlesByBiz,
    parseSearchResults,
    getAllArticlesByBiz
};
