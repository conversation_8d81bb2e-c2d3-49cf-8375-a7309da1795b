const axios = require('axios');

// 配置axios
const axiosInstance = axios.create({
    timeout: 30000,
    headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
});

// 根据公众号ID和日期范围获取文章列表
async function getArticlesByBizAndDateRange(bizId, startDate, endDate) {
    try {
        console.log(`获取公众号 ${bizId} 从 ${startDate} 到 ${endDate} 的文章`);
        
        // 注意：这是一个模拟实现
        // 实际的微信公众号文章获取需要通过微信的API或者爬虫技术
        // 由于微信的反爬虫机制，这里提供一个基础框架
        
        const articles = [];
        
        // 方法1: 尝试通过搜索API获取（需要有效的token）
        // 这个方法在实际环境中可能需要更复杂的认证和处理
        
        // 方法2: 模拟数据（用于演示）
        // 在实际项目中，你可能需要：
        // 1. 使用微信公众平台的API
        // 2. 使用第三方服务
        // 3. 实现更复杂的爬虫逻辑
        
        // 这里返回一个示例数据结构
        // 在实际项目中，这里应该调用微信API或爬虫获取真实数据
        const mockArticles = [
            {
                title: '如何使用微信公众号文章下载工具',
                url: `https://mp.weixin.qq.com/s?__biz=${bizId}&mid=2247484001&idx=1&sn=abc123&chksm=ea123456`,
                publishTime: '2025-07-10',
                digest: '详细介绍微信公众号文章下载工具的使用方法和技巧'
            },
            {
                title: 'Markdown格式文档编写指南',
                url: `https://mp.weixin.qq.com/s?__biz=${bizId}&mid=2247484002&idx=1&sn=def456&chksm=ea123457`,
                publishTime: '2025-07-11',
                digest: '全面介绍Markdown语法和最佳实践，让你快速掌握文档编写技能'
            },
            {
                title: '前端开发最佳实践分享',
                url: `https://mp.weixin.qq.com/s?__biz=${bizId}&mid=2247484003&idx=1&sn=ghi789&chksm=ea123458`,
                publishTime: '2025-07-12',
                digest: '分享前端开发中的常见问题和解决方案，提升开发效率'
            }
        ];
        
        // 过滤日期范围
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        const filteredArticles = mockArticles.filter(article => {
            const publishDate = new Date(article.publishTime);
            return publishDate >= start && publishDate <= end;
        });
        
        console.log(`找到 ${filteredArticles.length} 篇符合条件的文章`);
        return filteredArticles;
        
    } catch (error) {
        console.error('获取文章列表失败:', error);
        throw error;
    }
}

// 尝试通过微信搜索API获取文章（需要进一步实现）
async function searchArticlesByBiz(bizId, offset = 0, count = 10) {
    try {
        // 这个方法需要有效的微信token和参数
        // 实际实现时需要处理认证、cookie等问题
        
        const searchUrl = 'https://mp.weixin.qq.com/mp/profile_ext';
        const params = {
            action: 'getmsg',
            __biz: bizId,
            f: 'json',
            offset: offset,
            count: count,
            is_ok: '1',
            scene: '124'
            // 还需要其他认证参数
        };
        
        // 注意：这个请求在没有正确认证的情况下会失败
        // const response = await axiosInstance.get(searchUrl, { params });
        // return response.data;
        
        // 返回空结果，避免实际请求失败
        return {
            general_msg_list: JSON.stringify({
                list: []
            })
        };
        
    } catch (error) {
        console.error('搜索文章失败:', error);
        return {
            general_msg_list: JSON.stringify({
                list: []
            })
        };
    }
}

// 解析搜索结果
function parseSearchResults(searchData) {
    try {
        if (!searchData.general_msg_list) {
            return [];
        }
        
        const msgList = JSON.parse(searchData.general_msg_list);
        const articles = [];
        
        if (msgList.list && Array.isArray(msgList.list)) {
            msgList.list.forEach(item => {
                if (item.app_msg_ext_info) {
                    const article = item.app_msg_ext_info;
                    articles.push({
                        title: article.title,
                        url: article.content_url,
                        publishTime: new Date(item.comm_msg_info.datetime * 1000).toISOString().split('T')[0],
                        digest: article.digest,
                        author: article.author
                    });
                    
                    // 处理多图文消息
                    if (article.multi_app_msg_item_list) {
                        article.multi_app_msg_item_list.forEach(subArticle => {
                            articles.push({
                                title: subArticle.title,
                                url: subArticle.content_url,
                                publishTime: new Date(item.comm_msg_info.datetime * 1000).toISOString().split('T')[0],
                                digest: subArticle.digest,
                                author: subArticle.author
                            });
                        });
                    }
                }
            });
        }
        
        return articles;
        
    } catch (error) {
        console.error('解析搜索结果失败:', error);
        return [];
    }
}

// 获取所有文章（分页处理）
async function getAllArticlesByBiz(bizId, startDate, endDate, maxPages = 10) {
    const allArticles = [];
    let offset = 0;
    const count = 10;
    
    for (let page = 0; page < maxPages; page++) {
        try {
            const searchData = await searchArticlesByBiz(bizId, offset, count);
            const articles = parseSearchResults(searchData);
            
            if (articles.length === 0) {
                break; // 没有更多文章
            }
            
            // 过滤日期范围
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            const filteredArticles = articles.filter(article => {
                const publishDate = new Date(article.publishTime);
                return publishDate >= start && publishDate <= end;
            });
            
            allArticles.push(...filteredArticles);
            
            // 如果最早的文章已经超出日期范围，停止搜索
            const oldestArticle = articles[articles.length - 1];
            if (oldestArticle && new Date(oldestArticle.publishTime) < start) {
                break;
            }
            
            offset += count;
            
            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.error(`获取第 ${page + 1} 页文章失败:`, error);
            break;
        }
    }
    
    return allArticles;
}

module.exports = {
    getArticlesByBizAndDateRange,
    searchArticlesByBiz,
    parseSearchResults,
    getAllArticlesByBiz
};
