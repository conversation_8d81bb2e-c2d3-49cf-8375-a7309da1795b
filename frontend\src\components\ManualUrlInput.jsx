import React, { useState } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Space, 
  message, 
  Typography, 
  Divider,
  Alert
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  FileTextOutlined 
} from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;
const API_BASE_URL = 'http://localhost:3001/api';

const ManualUrlInput = ({ onArticlesLoaded, onStatusChange }) => {
  const [urlText, setUrlText] = useState('');
  const [loading, setLoading] = useState(false);

  // 处理URL列表
  const handleProcessUrls = async () => {
    if (!urlText.trim()) {
      message.error('请输入文章URL');
      return;
    }

    // 解析URL列表
    const urls = urlText
      .split('\n')
      .map(url => url.trim())
      .filter(url => url && url.startsWith('http'));

    if (urls.length === 0) {
      message.error('请输入有效的URL（每行一个）');
      return;
    }

    setLoading(true);
    onStatusChange({ 
      type: 'loading', 
      message: `正在处理 ${urls.length} 个URL...` 
    });

    try {
      const response = await axios.post(`${API_BASE_URL}/article/list-from-urls`, {
        urls: urls
      }, {
        timeout: 60000 // 60秒超时
      });

      if (response.data.success) {
        const { articles, total, message: responseMessage } = response.data.data;
        
        if (onArticlesLoaded) {
          onArticlesLoaded(articles);
        }
        
        message.success(`${responseMessage}！`);
        onStatusChange({ 
          type: 'success', 
          message: `成功处理 ${total} 个URL，请在右侧选择要下载的文章` 
        });
      } else {
        throw new Error(response.data.message || '处理失败');
      }
    } catch (error) {
      console.error('处理URL列表失败:', error);
      let errorMessage = '处理URL列表失败';
      
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请减少URL数量或检查网络连接';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      message.error(errorMessage);
      onStatusChange({ 
        type: 'error', 
        message: `处理URL列表失败: ${errorMessage}` 
      });
    } finally {
      setLoading(false);
    }
  };

  // 添加示例URL
  const addExampleUrls = () => {
    const examples = [
      'https://mp.weixin.qq.com/s/S5SKpzQ8GFGzv6dO6efnFA',
      'https://mp.weixin.qq.com/s/example2',
      'https://mp.weixin.qq.com/s/example3'
    ];
    
    setUrlText(examples.join('\n'));
  };

  // 清空输入
  const clearInput = () => {
    setUrlText('');
  };

  return (
    <Card title="手动输入文章URL" size="default">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <Alert
          message="真实文章获取"
          description="由于微信的反爬虫机制，自动获取文章列表可能失败。您可以手动提供文章URL列表来获取真实的文章数据。"
          type="info"
          showIcon
        />

        <div>
          <Text strong>输入文章URL列表（每行一个）：</Text>
          <TextArea
            placeholder={`请输入微信文章URL，每行一个，例如：
https://mp.weixin.qq.com/s/S5SKpzQ8GFGzv6dO6efnFA
https://mp.weixin.qq.com/s/example2
https://mp.weixin.qq.com/s/example3`}
            value={urlText}
            onChange={(e) => setUrlText(e.target.value)}
            rows={6}
            style={{ fontSize: '12px' }}
          />
        </div>

        <Space>
          <Button
            type="primary"
            icon={<FileTextOutlined />}
            loading={loading}
            onClick={handleProcessUrls}
            disabled={!urlText.trim()}
          >
            处理URL列表
          </Button>
          <Button
            icon={<PlusOutlined />}
            onClick={addExampleUrls}
          >
            添加示例
          </Button>
          <Button
            icon={<DeleteOutlined />}
            onClick={clearInput}
            disabled={!urlText.trim()}
          >
            清空
          </Button>
        </Space>

        <Divider />

        <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.5' }}>
          <Paragraph>
            <Text strong>使用说明：</Text>
          </Paragraph>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>每行输入一个完整的微信文章URL</li>
            <li>支持短链接格式（/s/xxxxx）和完整URL格式</li>
            <li>系统会自动解析每篇文章的标题、作者等信息</li>
            <li>即使某些文章解析失败，仍可尝试下载</li>
            <li>处理完成后，文章列表会显示在右侧</li>
          </ul>
          
          <Paragraph style={{ marginTop: '12px' }}>
            <Text strong>获取URL的方法：</Text>
          </Paragraph>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>在微信中打开文章，点击右上角"..."，选择"复制链接"</li>
            <li>在公众号历史消息中逐个复制文章链接</li>
            <li>使用浏览器访问公众号主页，复制文章链接</li>
          </ul>
        </div>
      </Space>
    </Card>
  );
};

export default ManualUrlInput;
