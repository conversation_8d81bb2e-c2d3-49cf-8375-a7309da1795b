const axios = require('axios');
const cheerio = require('cheerio');
const { JSDOM } = require('jsdom');
const TurndownService = require('turndown');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 配置axios
const axiosInstance = axios.create({
    timeout: 30000,
    headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
});

// 解析微信文章
async function parseWechatArticle(url) {
    try {
        console.log('开始解析文章:', url);

        const response = await axiosInstance.get(url);
        const html = response.data;

        const $ = cheerio.load(html);

        // 提取文章标题 - 多种选择器
        let title = '';
        const titleSelectors = [
            '#activity-name',
            '.rich_media_title',
            'h1.rich_media_title',
            'h2.rich_media_title',
            '.weui-msg__title',
            'h1',
            'title'
        ];

        for (const selector of titleSelectors) {
            title = $(selector).text().trim();
            if (title && title !== '微信公众平台') {
                break;
            }
        }

        if (!title) {
            title = '未知标题';
        }

        // 提取作者/公众号名称 - 多种选择器
        let author = '';
        const authorSelectors = [
            '#js_name',
            '.rich_media_meta_nickname',
            '.profile_nickname',
            '.weui-msg__desc',
            'a[id="js_name"]'
        ];

        for (const selector of authorSelectors) {
            author = $(selector).text().trim();
            if (author) {
                break;
            }
        }

        if (!author) {
            author = '未知作者';
        }

        // 提取发布时间 - 多种选择器和格式
        let publishDate = '';
        const dateSelectors = [
            '#publish_time',
            '.rich_media_meta_text',
            '.publish_time',
            'em[id="publish_time"]'
        ];

        for (const selector of dateSelectors) {
            const dateText = $(selector).text().trim();
            if (dateText) {
                publishDate = dateText;
                break;
            }
        }

        if (!publishDate) {
            publishDate = new Date().toISOString().split('T')[0];
        }

        // 提取文章内容 - 多种选择器
        let contentElement = null;
        const contentSelectors = [
            '#js_content',
            '.rich_media_content',
            '.weui-msg__text-area',
            '.rich_media_area_primary'
        ];

        for (const selector of contentSelectors) {
            contentElement = $(selector);
            if (contentElement.length > 0) {
                break;
            }
        }

        if (!contentElement || contentElement.length === 0) {
            throw new Error('无法找到文章内容');
        }

        // 提取图片URLs
        const images = [];
        contentElement.find('img').each((i, img) => {
            const $img = $(img);
            let src = $img.attr('data-src') || $img.attr('src') || $img.attr('data-original');

            // 处理微信图片URL
            if (src) {
                // 如果是相对路径，转换为绝对路径
                if (src.startsWith('//')) {
                    src = 'https:' + src;
                } else if (src.startsWith('/')) {
                    src = 'https://mmbiz.qpic.cn' + src;
                }

                if (src.startsWith('http')) {
                    images.push({
                        url: src,
                        alt: $img.attr('alt') || '',
                        index: i
                    });
                }
            }
        });

        // 获取纯HTML内容用于转换
        const contentHtml = contentElement.html();

        console.log(`解析完成: 标题=${title}, 作者=${author}, 图片数量=${images.length}`);

        return {
            title,
            author,
            publishDate,
            content: contentHtml,
            images,
            sourceUrl: url
        };

    } catch (error) {
        console.error('解析文章失败:', error);
        throw error;
    }
}

// 下载图片
async function downloadImages(images, articleTitle, customDir = null) {
    const imagesInfo = [];

    if (!images || images.length === 0) {
        console.log('没有图片需要下载');
        return imagesInfo;
    }

    // 创建图片目录
    const baseDir = customDir || path.join(__dirname, '../downloads');
    const imagesDir = customDir || path.join(baseDir, 'images');

    if (!fs.existsSync(imagesDir)) {
        fs.mkdirSync(imagesDir, { recursive: true });
    }

    console.log(`开始下载 ${images.length} 张图片到目录: ${imagesDir}`);

    for (let i = 0; i < images.length; i++) {
        try {
            const image = images[i];
            console.log(`下载图片 ${i + 1}/${images.length}:`, image.url);

            // 设置请求头，模拟浏览器
            const response = await axiosInstance.get(image.url, {
                responseType: 'arraybuffer',
                timeout: 15000,
                headers: {
                    'Referer': 'https://mp.weixin.qq.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
            });

            // 获取文件扩展名
            const contentType = response.headers['content-type'];
            let ext = '.jpg';
            if (contentType) {
                if (contentType.includes('png')) ext = '.png';
                else if (contentType.includes('gif')) ext = '.gif';
                else if (contentType.includes('webp')) ext = '.webp';
                else if (contentType.includes('jpeg')) ext = '.jpg';
            }

            // 也可以从URL中推断扩展名
            if (ext === '.jpg') {
                const urlExt = path.extname(new URL(image.url).pathname);
                if (urlExt && ['.png', '.gif', '.webp', '.jpg', '.jpeg'].includes(urlExt.toLowerCase())) {
                    ext = urlExt.toLowerCase();
                }
            }

            // 生成安全的文件名
            const safeTitle = articleTitle.replace(/[<>:"/\\|?*]/g, '_').substring(0, 50);
            const filename = `${safeTitle}_image_${i + 1}${ext}`;
            const filepath = path.join(imagesDir, filename);

            // 保存图片
            fs.writeFileSync(filepath, response.data);
            console.log(`图片保存成功: ${filename}`);

            imagesInfo.push({
                originalUrl: image.url,
                localPath: customDir ? `images/${filename}` : `./images/${filename}`,
                filename: filename,
                alt: image.alt
            });

        } catch (error) {
            console.error(`下载图片失败 (${i + 1}/${images.length}):`, error.message);
            // 如果图片下载失败，保留原始URL
            const image = images[i];
            imagesInfo.push({
                originalUrl: image.url,
                localPath: image.url,
                filename: '',
                alt: image.alt
            });
        }
    }

    console.log(`图片下载完成，成功: ${imagesInfo.filter(img => img.filename).length}/${images.length}`);
    return imagesInfo;
}

// 生成Markdown
function generateMarkdown(articleData, imagesInfo) {
    try {
        console.log('开始生成Markdown...');

        // 配置turndown
        const turndownService = new TurndownService({
            headingStyle: 'atx',
            codeBlockStyle: 'fenced',
            bulletListMarker: '-'
        });

        // 自定义规则处理图片
        turndownService.addRule('images', {
            filter: 'img',
            replacement: function(content, node) {
                const src = node.getAttribute('data-src') ||
                           node.getAttribute('src') ||
                           node.getAttribute('data-original');
                const alt = node.getAttribute('alt') || '';

                // 查找对应的本地图片
                const imageInfo = imagesInfo.find(img => img.originalUrl === src);
                const finalSrc = imageInfo ? imageInfo.localPath : src;

                return `\n\n![${alt}](${finalSrc})\n\n`;
            }
        });

        // 自定义规则处理链接
        turndownService.addRule('links', {
            filter: 'a',
            replacement: function(content, node) {
                const href = node.getAttribute('href');
                if (!href || href === '#') {
                    return content;
                }
                return `[${content}](${href})`;
            }
        });

        // 自定义规则处理强调文本
        turndownService.addRule('emphasis', {
            filter: ['strong', 'b'],
            replacement: function(content) {
                return `**${content}**`;
            }
        });

        // 自定义规则处理斜体
        turndownService.addRule('italic', {
            filter: ['em', 'i'],
            replacement: function(content) {
                return `*${content}*`;
            }
        });

        // 转换HTML为Markdown
        let markdown = turndownService.turndown(articleData.content);

        // 清理多余的空行
        markdown = markdown.replace(/\n{3,}/g, '\n\n');

        // 生成Front Matter
        const frontMatter = `---
title: "${articleData.title.replace(/"/g, '\\"')}"
author: "${articleData.author.replace(/"/g, '\\"')}"
publish_date: "${articleData.publishDate}"
source_url: "${articleData.sourceUrl}"
images_count: ${imagesInfo.length}
downloaded_at: "${new Date().toISOString()}"
---

`;

        const finalMarkdown = frontMatter + markdown;
        console.log('Markdown生成完成');

        return finalMarkdown;

    } catch (error) {
        console.error('生成Markdown失败:', error);
        throw error;
    }
}

module.exports = {
    parseWechatArticle,
    downloadImages,
    generateMarkdown
};
